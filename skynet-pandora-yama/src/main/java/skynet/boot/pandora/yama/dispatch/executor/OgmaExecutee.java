package skynet.boot.pandora.yama.dispatch.executor;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.util.MultiValueMap;

import skynet.boot.pandora.MqServiceContextHolder;
import skynet.boot.pandora.api.*;
import skynet.boot.pandora.brave.TraceUtils;
import skynet.boot.pandora.brave.data.TraceBase;
import skynet.boot.pandora.brave.data.TraceHeaderUtils;
import skynet.boot.pandora.ogma.PandoraApiRequestObserverBuilder;
import skynet.boot.pandora.yama.action.ProcessDesignData;
import skynet.boot.pandora.yama.config.YamaProperties;
import skynet.boot.pandora.yama.core.ExecuteContext;
import skynet.boot.pandora.yama.core.ObservableExecutable;
import skynet.boot.pandora.yama.exception.YamaException;
import skynet.boot.pandora.yama.util.YamaHeaderUtil;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Ogma 执行器，支持 HTTP、WebSocket、gRPC、MQ 四种形式的接口调用，内置服务发现机制
 */
@Slf4j
public class OgmaExecutee implements ObservableExecutable {

    private final YamaProperties yamaProperties;
    private final TraceUtils traceUtils;
    private final PandoraApiRequestObserverBuilder pandoraApiRequestObserverBuilder;
    private final AtomicLong sendTime = new AtomicLong(System.nanoTime());

    public OgmaExecutee(YamaProperties yamaProperties, TraceUtils traceUtils, PandoraApiRequestObserverBuilder pandoraApiRequestObserverBuilder) {
        this.yamaProperties = yamaProperties;
        this.traceUtils = traceUtils;
        this.pandoraApiRequestObserverBuilder = pandoraApiRequestObserverBuilder;
    }

    @Override
    public ApiRequestObserver execute(String traceId, ApiResponseObserver apiResponseObserver, ExecuteContext executeContext) {

        ApiResponseObserver responseObserver = new ApiResponseObserver() {

            @Override
            public void onReceive(ApiResponse apiResponse) {
                apiResponse.getHeader().put("index", sendTime.get());
                apiResponseObserver.onReceive(apiResponse);
            }

            @Override
            public void onError(Throwable e) {
                apiResponseObserver.onError(e);
                apiResponseObserver.onCompleted();
            }

            @Override
            public void onCompleted() {
                apiResponseObserver.onCompleted();
            }
        };

        final ApiRequestObserver requestObserver = buildApiRequestObserver(traceId, responseObserver, executeContext);

        return new ApiRequestObserver() {

            @Override
            public void onSend(ApiRequest apiRequest, ApiSession session) {
                try {
                    if (requestObserver != null) {
                        requestObserver.onSend(apiRequest, session);
                        if (apiRequest.getHeader().getLong("index") != null) {
                            sendTime.set(apiRequest.getHeader().getLong("index"));
                        } else {
                            sendTime.set(System.nanoTime());
                        }
                    }
                } catch (Exception e) {
                    apiResponseObserver.onError(e);
                }
            }

            @Override
            public void onCancel() {
                if (requestObserver != null) {
                    requestObserver.onCancel();
                }
            }

            @Override
            public void onCompleted() {
                if (requestObserver != null) {
                    requestObserver.onCompleted();
                } else {
                    apiResponseObserver.onCompleted();
                }
            }
        };
    }

    /**
     * 构造 ApiRequestObserver
     */
    private ApiRequestObserver buildApiRequestObserver(String traceId, ApiResponseObserver apiResponseObserver, ExecuteContext executeContext) {
        ApiRequestObserver requestObserver = null;
        try {
            String uri = buildOgmaUri(executeContext);
            Map<String, String> headers = buildOgmaHeaders(traceId, executeContext);
            requestObserver = pandoraApiRequestObserverBuilder.build(uri, headers, apiResponseObserver);
        } catch (Exception e) {
            apiResponseObserver.onError(e);
        }
        return requestObserver;
    }

    /**
     * 构造请求头
     */
    private Map<String, String> buildOgmaHeaders(String traceId, ExecuteContext executeContext) {
        if (StringUtils.isBlank(traceId)) {
            return new HashMap<>();
        }
        Map<String, String> result = new HashMap<>();
        // 构造 mq headers
        Map<String, String> properties = MqServiceContextHolder.getMqMessage() == null ? null : MqServiceContextHolder.getMqMessage().getProperties();
        if (properties != null) {
            result.putAll(properties);
        }
        // 构造 trace headers
        TraceBase traceBase = traceUtils.getTraceBase();
        JSONObject context = traceBase == null ? null : traceBase.getContext();
        MultiValueMap<String, String> headers = TraceHeaderUtils.buildHeader(traceId, executeContext.getCurrentNode().getCode(), context);
        headers.entrySet().stream()
                .filter(entry -> !entry.getValue().isEmpty())
                .forEach(entry -> result.put(entry.getKey(), entry.getValue().get(0)));;
        // 如果是异步节点，构造异步调用的请求头
        if (executeContext.getCurrentNode().getNodeInfo().getAsync() != null && executeContext.getCurrentNode().getNodeInfo().getAsync()) {
            YamaHeaderUtil.setYamaHeaders(result, executeContext.getContextId(), 
                executeContext.getCurrentNode().getCode(), yamaProperties.getAsync().getCallbackUri());
        }
        return result;
    }

    /**
     * 从执行上下文中获取请求地址
     */
    private String buildOgmaUri(ExecuteContext executeContext) {
        ProcessDesignData.NodeListBean requestInfo = executeContext.getCurrentNode().getNodeInfo();

        if (StringUtils.isBlank(requestInfo.getServerInterface())) {
            throw new YamaException(-1, "the serverInterface is blank.");
        }

        // serverInterface 配置了绝对地址，直接使用，无需服务发现
        if (isAbsoluteUrl(requestInfo.getServerInterface())) {
            return requestInfo.getServerInterface();
        }

        // 服务名：lb:service-name
        if (StringUtils.isBlank(requestInfo.getActionPoint())) {
            throw new YamaException(-1, "the actionPoint is blank.");
        }

        // 兼容老平台格式：action-code@plugin-code
        if (requestInfo.getActionPoint().contains("@")) {
            String[] ss = requestInfo.getActionPoint().split("@");
            requestInfo.setActionPoint("lb:" + ss[0]);
            requestInfo.setProtocol("http");
        }

        // 协议：http/https/ws/wss/gprc
        if (StringUtils.isBlank(requestInfo.getProtocol())) {
            throw new YamaException(-1, "the protocol is blank.");
        }

        // 没有配置绝对地址，则需要通过服务名从注册中心获取地址
        String ogmaUrl = String.format("%s://%s%s", requestInfo.getProtocol(), requestInfo.getActionPoint(), requestInfo.getServerInterface());
        log.debug("Build ogmaUrl: " + ogmaUrl);
        return ogmaUrl;
    }

    /**
     * 判断某个 url 是否为绝对路径
     */
    private boolean isAbsoluteUrl(String url) {
        return isHttp(url) || isWebSocket(url) || isGrpc(url) || isMq(url);
    }

    /**
     * 判断某个 url 是否为 HTTP 协议
     */
    private boolean isHttp(String url) {
        return url.startsWith("http://") || url.startsWith("https://");
    }

    /**
     * 判断某个 url 是否为 WebSocket 协议
     */
    private boolean isWebSocket(String url) {
        return url.startsWith("ws://") || url.startsWith("wss://");
    }

    /**
     * 判断某个 url 是否为 gRPC 协议
     */
    private boolean isGrpc(String url) {
        return url.startsWith("grpc://");
    }

    /**
     * 判断某个 url 是否为 MQ 协议
     */
    private boolean isMq(String url) {
        return url.startsWith("mq://");
    }

}
