package skynet.boot.pandora.ogma.tango;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.msgpack.jackson.dataformat.MessagePackMapper;
import org.springframework.beans.BeanUtils;
import skynet.boot.pandora.api.*;
import skynet.boot.pandora.api.mq.domain.MqMessage;
import skynet.boot.pandora.api.mq.domain.MqMessageConst;
import skynet.boot.pandora.api.mq.domain.MqParam;
import skynet.boot.pandora.ogma.core.ApiRequestObserverBuilder;
import skynet.boot.pandora.ogma.tango.config.OgmaMqProperties;
import skynet.boot.pandora.ogma.tango.domain.MqMessageProps;

import java.net.URI;
import java.util.Map;


/**
 * <AUTHOR>
 */
@Slf4j
public class ApiRequestObserver4MqBuilder implements ApiRequestObserverBuilder {

    private final PandoraMqProducer pandoraMqProducer;
    private final OgmaMqProperties ogmaMqProperties;
    private final ObjectMapper objectMapper = new MessagePackMapper();

    public ApiRequestObserver4MqBuilder(OgmaMqProperties ogmaMqProperties, PandoraMqProducer pandoraMqProducer) {
        this.ogmaMqProperties = ogmaMqProperties;
        this.pandoraMqProducer = pandoraMqProducer;
    }

    @Override
    public ApiProtocol getApiProtocol() {
        return ApiProtocol.MQ;
    }


    /**
     * @param url                 mq://target_topic/endpoint
     * @param header
     * @param apiResponseObserver
     * @return
     */
    @Override
    public ApiRequestObserver build(URI url, Map<String, String> header, ApiResponseObserver apiResponseObserver) {
        checkArgument(url, apiResponseObserver);
        log.debug("Build MQ ApiRequestObserver with url = {}", url);

        MqParam targetQParam = new MqParam();
        BeanUtils.copyProperties(ogmaMqProperties, targetQParam);
        targetQParam.setOutq(url.toString());
        log.debug("TargetQParam = {}", targetQParam);
        return new ApiRequestObserver() {
            @Override
            public void onSend(ApiRequest apiRequest, ApiSession session) throws Exception {
                log.debug("Send apiRequest = {}", apiRequest);
                MqMessage mqMessage = new MqMessage();
                header.forEach((k, v) -> mqMessage.getProperties().put(k, v));
                if (session instanceof MqMessageProps mqMessageProps) {
                    if (mqMessageProps.getHeaders() != null) {
                        mqMessage.getProperties().putAll(mqMessageProps.getHeaders());
                    }
                }
                mqMessage.setBody(objectMapper.writeValueAsBytes(apiRequest));
                mqMessage.putProperty(MqMessageConst.HEADER_MESSAGE_FORMAT, MqMessageConst.HEADER_MESSAGE_FORMAT_MSGPACK);
                log.debug("MqMessage = {}", mqMessage);
                try {
                    pandoraMqProducer.send(targetQParam, mqMessage);
                } catch (Exception e) {
                    log.error("Send error.", e);
                    apiResponseObserver.onError(e);
                }
            }

            @Override
            public void onCancel() {
                log.debug("onCancel");
                apiResponseObserver.onCompleted();
            }

            @Override
            public void onCompleted() {
                log.debug("OnCompleted");
                apiResponseObserver.onCompleted();
            }
        };
    }

    @Override
    public void close() throws Exception {
        log.debug("Close.");
    }
}
