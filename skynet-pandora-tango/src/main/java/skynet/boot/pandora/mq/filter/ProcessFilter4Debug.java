package skynet.boot.pandora.mq.filter;

import cn.hutool.core.io.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.env.Environment;
import skynet.boot.SkynetProperties;
import skynet.boot.common.ShmDirUtils;
import skynet.boot.pandora.api.ApiRequest;
import skynet.boot.pandora.api.ApiResponse;
import skynet.boot.pandora.api.mq.domain.MqMessage;
import skynet.boot.pandora.api.mq.domain.MqParam;

import java.io.File;

/**
 * 消息调试过滤器
 * <p>
 * 主要目的:
 * 在有的消息处理过程中，会引起进程直接宕机，无法定位到具体的消息。
 * <p>
 * 现在将每条消息处理前，存入零时文件系统，处理结束后（包括处理异常，只要不宕机的）再把消息删除。
 * 如果进程宕机后，异常的目标消息，就在零时目录中，方便快速定位.
 * <p>
 * 一般在生产环境下调试使用，有将内存耗尽的风险。
 * <p>
 * 备注：
 * Linux 下的 缺省目录:/dev/shm, 可以通过 skynet.dev.shm.path 系统属性修改。
 * 其他系统下: java.io.tmpdir 目录下。
 *
 * <AUTHOR>
 * @date 2019-10-22 21:14
 */
@Slf4j
@RefreshScope
public class ProcessFilter4Debug implements ProcessFilter {

    @Value("${skynet.mq.doing.message.store.debug:false}")
    private boolean storeDoingMessageDebug;

    private final SkynetProperties skynetProperties;
    private final ShmDirUtils shmDirUtils;

    private File tempFileRoot;

    public ProcessFilter4Debug(SkynetProperties skynetProperties, Environment environment) {
        this.skynetProperties = skynetProperties;
        this.shmDirUtils = new ShmDirUtils(environment);

        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            try {
                close();
            } catch (Exception ignored) {
            }
        }));
    }

    private File getTempFileRoot() throws Exception {
        if (tempFileRoot == null) {
            synchronized (ProcessFilter4Debug.class) {
                if (tempFileRoot == null) {
                    tempFileRoot = shmDirUtils.getShmDir("skynet.mq.message.debug/" + skynetProperties.getActionId());
                    log.info("Message store temp dir: [{}]", tempFileRoot);
                }
            }
        }
        return tempFileRoot;
    }

    @Override
    public boolean before(MqParam fromMqParam, MqMessage srcMessage, ApiRequest apiRequest) {
        if (storeDoingMessageDebug) {
            log.debug("Store doing message to file...");
            try {
                File file = new File(getTempFileRoot(), String.format("%s.json", srcMessage.getTraceId()));
                FileUtil.writeString(srcMessage.toString(), file, "utf-8");
                FileUtil.writeString("\r\n", file, "utf-8");
                FileUtil.writeString(fromMqParam == null ? "mqParam is null" : fromMqParam.toString(), file, "utf-8");
            } catch (Exception e) {
                log.error("Store doing message to file error = {}", e.getMessage());
            }
        }
        return true;
    }

    @Override
    public boolean after(MqParam fromMqParam, MqMessage targetMessage, ApiRequest apiRequest, ApiResponse apiResponse)
            throws Exception {
        if (storeDoingMessageDebug) {
            try {
                log.debug("Delete done message file...");
                File file = new File(getTempFileRoot(), String.format("%s.json", targetMessage.getTraceId()));
                FileUtil.del(file);
            } catch (Exception e) {
                log.error("Delete done message file error = {}", e.getMessage());
            }
        }
        return true;
    }

    @Override
    public void close() throws Exception {
        try {
            log.debug("Delete store doing message fire dir...");
            FileUtil.del(getTempFileRoot());
        } catch (Exception e) {
            log.error("Delete store doing message fire error = {}", e.getMessage());
        }
    }
}
//
// public static void main(String[] args) throws Exception {
// SkynetProperties skynetProperties = new SkynetProperties();
// skynetProperties.setActionId("beehive");
// ProcessFilter4Debug debugFilter = new ProcessFilter4Debug(skynetProperties);
//
// String trackId = UUID.randomUUID().toString();
// MqMessage mqMessage = new MqMessage();
// mqMessage.setBody(" message body").setBizId("bizId").setOK(true);
// MqParam mqParam = new MqParam().setMqUri("mqurl").setInq("inq");
//
// debugFilter.before(trackId, mqMessage, mqParam);
//
// debugFilter.after(trackId, null, null, null);
//
// trackId = UUID.randomUUID().toString();
// debugFilter.before(trackId, mqMessage, mqParam);
//
// trackId = UUID.randomUUID().toString();
// debugFilter.before(trackId, mqMessage, mqParam);
// debugFilter.after(trackId, null, null, null);
//
// trackId = UUID.randomUUID().toString();
// debugFilter.before(trackId, mqMessage, mqParam);
//
// Thread.sleep(10000000);
// //debugFilter.close();
// }