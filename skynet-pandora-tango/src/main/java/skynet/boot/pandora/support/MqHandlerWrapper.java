package skynet.boot.pandora.support;

import com.alibaba.fastjson2.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Stopwatch;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.msgpack.jackson.dataformat.MessagePackMapper;
import org.springframework.util.Assert;
import skynet.boot.SkynetProperties;
import skynet.boot.common.OsUtil;
import skynet.boot.exception.SkynetException;
import skynet.boot.pandora.MqServiceContextHolder;
import skynet.boot.pandora.MqServiceHandler;
import skynet.boot.pandora.api.ApiRequest;
import skynet.boot.pandora.api.ApiResponse;
import skynet.boot.pandora.api.mq.domain.MqMessage;
import skynet.boot.pandora.api.mq.domain.MqMessageConst;
import skynet.boot.pandora.api.mq.domain.MqParam;
import skynet.boot.pandora.brave.core.SpanContext;
import skynet.boot.pandora.brave.core.TraceBaseContext;
import skynet.boot.pandora.brave.data.TraceHeader;
import skynet.boot.pandora.brave.metric.config.PandoraMetricProperties;
import skynet.boot.pandora.mq.filter.ProcessFilter;
import skynet.boot.pandora.ogma.PandoraApiRequestObserverBuilder;
import skynet.boot.pandora.ogma.tango.PandoraMqProducer;
import skynet.boot.pandora.service.core.HandlerWrapper;
import skynet.boot.pandora.service.support.PandoraExecutorService;

import java.util.*;

/**
 * MQ消息处理器 容器
 * <p>
 * 每个消息监听器消息线程 分配一个MsgProcessor
 * <p>
 * <p>
 * 入口：init（svcName） 备注： spring 注解
 * <p>
 * {svcName}.context {svcName}.handler
 *
 * <AUTHOR>
 */
@Slf4j
public final class MqHandlerWrapper extends HandlerWrapper implements AutoCloseable {

    private final Stopwatch eventStopwatch = Stopwatch.createStarted();
    private final PandoraMqProducer pandoraMqProducer;
    private final List<ProcessFilter> processFilterList;
    private final Map<String, String> fromHeader;
    private final String metricName;
    private final MeterRegistry meterRegistry;
    private MqServiceHandler serviceHandler;
    private final ObjectMapper objectMapper = new MessagePackMapper();

    private final MqSessionContext mqSessionContext;

    public MqHandlerWrapper(PandoraExecutorService pandoraExecutorService, SkynetProperties skynetProperties,
                            PandoraMqProducer pandoraMqProducer, List<ProcessFilter> processFilterList,
                            MeterRegistry meterRegistry, PandoraApiRequestObserverBuilder pandoraApiRequestObserverBuilder) {
        super(pandoraExecutorService);
        this.pandoraMqProducer = pandoraMqProducer;
        this.processFilterList = processFilterList;
        this.processFilterList.sort(Comparator.comparingInt(ProcessFilter::getOrder));
        this.meterRegistry = meterRegistry;
        this.metricName = String.format("%s.mq.requests", PandoraMetricProperties.DEFAULT_PREFIX);

        this.fromHeader = new HashMap<>(3);
        this.fromHeader.put(MqMessageConst.HEADER_FROM_PID, String.valueOf(OsUtil.getCurrentPid()));
        this.fromHeader.put(MqMessageConst.HEADER_FROM_IP, skynetProperties.getIpAddress());
        this.fromHeader.put(MqMessageConst.HEADER_FROM_HOST, OsUtil.getHostName());
        this.mqSessionContext = new MqSessionContext(skynetProperties, pandoraApiRequestObserverBuilder);
    }

    public void initialize(MqServiceHandler serviceHandler) throws Exception {
        Assert.notNull(serviceHandler, "MqServiceHandler is null.");
        this.serviceHandler = serviceHandler;
    }

    /**
     * 处理消息
     *
     * @param message
     * @param mqParam
     * @throws Exception
     */
    public void process(EndpointParam4Mq endpointParam, MqMessage message, MqParam mqParam) throws Exception {

//        //从消息头中获取对应的处理端点名称，如果有 端点，判断是否匹配，如果不匹配，直接返回  by lyhu
//        String targetEndpoint = message.getProperty(MqMessageConst.HEADER_TARGET_ENDPOINT);
//        if (StringUtils.isNotBlank(targetEndpoint) && !targetEndpoint.equalsIgnoreCase(String.format("mq://%s/%s", mqParam.getInq(), endpointParam.getPath()))) {
//            log.warn("The target endpoint = {} is not match the current endpoint = {}.", targetEndpoint, endpointParam.getName());
//            return;
//        }

        Map<String, String> metaData = new HashMap<>(message.getProperties());
        metaData.put(TraceHeader.PANDORA_TRACE_ID.getKey(), message.getTraceId());
        TraceBaseContext.init(SpanContext.fill(metaData));

        MqServiceContextHolder.setRequest(mqParam, message);
        long receiveTime = System.currentTimeMillis();
        // 设置当前线程的TrackId
        ApiResponse apiResponse = null;
        ApiRequest apiRequest = null;
        try {
            try {
                if (MqMessageConst.HEADER_MESSAGE_FORMAT_MSGPACK.equalsIgnoreCase(message.getProperty(MqMessageConst.HEADER_MESSAGE_FORMAT))) {
                    apiRequest = objectMapper.readValue(message.getBody(), ApiRequest.class);
                } else {
                    apiRequest = JSON.parseObject(message.getBody(), ApiRequest.class);
                }

                for (ProcessFilter filter : processFilterList) {
                    if (!filter.before(mqParam, message, apiRequest)) {
                        return;
                    }
                }
                this.eventStopwatch.reset().start();
                try {
                    this.mqSessionContext.init(message);
                    // 具体处理器处理
                    apiResponse = this.serviceHandler.process(mqSessionContext, apiRequest);
                } finally {
                    this.meterRegistry.timer(this.metricName, Tags.of("endpoint", endpointParam.getName()).and("event", "process")).record(this.eventStopwatch.elapsed());
                }

                if (apiResponse != null) {
                    apiResponse.setTraceId(apiRequest.getTraceId());
                }
            } catch (Exception e) {
                log.error("MqSvcHandler.process.error = {}", e.getMessage());
                apiResponse = new ApiResponse();
                apiResponse.setTraceId(message.getTraceId());
                apiResponse.getHeader().put("ExceptionTypeName", e.getClass().getTypeName());
                apiResponse.getHeader().setCode(e instanceof SkynetException ? ((SkynetException) e).getCode() : -1);
                apiResponse.getHeader().setMessage(ExceptionExt.getMergedMessage(e));
            }

            message.getProperties().remove(MqMessageConst.HEADER_TARGET_ENDPOINT);
            message.getProperties().putAll(fromHeader);
            message.putProperty(MqMessageConst.HEADER_PROCESS_COST,
                    String.valueOf(System.currentTimeMillis() - receiveTime));

            if (apiResponse != null) {
                log.debug("Act-out = {}", apiResponse);
                message.setBody(objectMapper.writeValueAsBytes(apiResponse));
                message.putProperty(MqMessageConst.HEADER_MESSAGE_FORMAT, MqMessageConst.HEADER_MESSAGE_FORMAT_MSGPACK);
            }

            for (ProcessFilter filter : processFilterList) {
                if (!filter.after(mqParam, message, apiRequest, apiResponse)) {
                    return;
                }
            }

            if (apiResponse == null) {
                // log.warn("[traceId = {}]process result is null", message.getTraceId());
                return;
            }

            if (mqSessionContext.send(apiResponse)) {
                mqSessionContext.close();
                return;
            }

            // 判断是否需要返回，
            if (StringUtils.isNotBlank(mqParam.getOutq())) {
                try {
                    this.pandoraMqProducer.send(mqParam, message);
                } catch (Exception e) {
                    log.error("Return message to q [{}] error.", mqParam.getOutq(), e);
                }
            }
        } catch (Exception e) {
            // TODO: 细分异常种类，如 可恢复异常，
            log.error("TraceId = {} process message error {}", message.getTraceId(), e.getMessage(), e);
            throw e;
        } finally {
            MqServiceContextHolder.clear();
            TraceBaseContext.clear();
        }
    }

    @Override
    public void close() throws Exception {
        log.debug("Close MqHandlerWrapper begin...");
        for (ProcessFilter filter : processFilterList) {
            try {
                filter.close();
            } catch (Exception ignored) {
            }
        }
        this.pandoraMqProducer.close();
        log.debug("Close MqHandlerWrapper end.");
    }

    static class ExceptionExt {
        public static String getMergedMessage(Throwable exp) {
            if (exp == null) {
                return null;
            }
            List<String> msgList = new ArrayList<>(1);
            fillMessage(msgList, exp);
            return StringUtils.join(msgList, ";");
        }

        private static void fillMessage(List<String> msgList, Throwable exp) {
            if (exp == null) {
                return;
            }
            msgList.add(exp.getMessage());
            if (exp.getCause() != null) {
                fillMessage(msgList, exp.getCause());
            }
        }
    }
}
