package skynet.boot.pandora.support;

import com.alibaba.fastjson2.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.jetbrains.annotations.NotNull;
import org.msgpack.jackson.dataformat.MessagePackMapper;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.util.Assert;
import skynet.boot.SkynetProperties;
import skynet.boot.common.Guid;
import skynet.boot.common.OsUtil;
import skynet.boot.pandora.api.mq.MqConsumer;
import skynet.boot.pandora.api.mq.MqListener;
import skynet.boot.pandora.api.mq.domain.MqMessage;
import skynet.boot.pandora.api.mq.domain.MqMessageConst;
import skynet.boot.pandora.api.mq.domain.MqParam;
import skynet.boot.pandora.mq.domain.MqMessageCtrl;
import skynet.boot.pandora.mq.domain.MqMessageStatus;
import skynet.boot.pandora.mq.exception.MessageServiceException;
import skynet.boot.pandora.ogma.tango.PandoraMqProducer;
import skynet.boot.pandora.support.config.MqProperties;

import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * MQ 消费者代理类
 * <p>
 * 该类负责创建和管理 MQ 消费者实例，处理消息监听和状态报告。
 * 它实现了 ApplicationContextAware 接口以获取 Spring 上下文，
 * 并实现了 AutoCloseable 接口以支持资源的自动关闭。
 *
 * <AUTHOR>
 */
@Slf4j
public class MqConsumerProxy implements ApplicationContextAware, AutoCloseable {

    private static final AtomicInteger MQ_CONSUMER_PROXY_INDEX = new AtomicInteger(0);
    private static final CountDownLatch APPLICATION_READY_LATCH = new CountDownLatch(1);

    private final String status = "heartbeat";
    private final MqProperties mqProperties;
    private final SkynetProperties skynetProperties;
    private final PandoraMqProducer pandoraMqProducer;
    private final MeterRegistry meterRegistry;
    private final ObjectMapper objectMapper = new MessagePackMapper();
    private ApplicationContext applicationContext;

    private volatile boolean isRunning = true;
    private MqListener mqListener;
    private MqConsumer mqConsumer;
    private MqConsumer cancelMqConsumer;
    @Getter
    private MqParam mqParam;

    private ScheduledExecutorService statusReportExecutor;

    /**
     * 构造函数
     *
     * @param mqProperties      MQ 配置属性
     * @param skynetProperties  Skynet 系统配置属性
     * @param pandoraMqProducer MQ 消息生产者
     * @param meterRegistry     指标注册表
     */
    public MqConsumerProxy(MqProperties mqProperties, SkynetProperties skynetProperties,
                           PandoraMqProducer pandoraMqProducer, MeterRegistry meterRegistry) {
        MQ_CONSUMER_PROXY_INDEX.incrementAndGet();
        this.mqProperties = Objects.requireNonNull(mqProperties, "mqProperties is null");
        this.skynetProperties = Objects.requireNonNull(skynetProperties, "skynetProperties is null");
        this.pandoraMqProducer = Objects.requireNonNull(pandoraMqProducer, "pandoraMqProducer is null");
        this.meterRegistry = Objects.requireNonNull(meterRegistry, "meterRegistry is null");
    }

    /**
     * 开始监听指定的 MQ 队列
     * <p>
     * 该方法创建一个 MQ 消费者实例，并开始监听指定的队列。
     * 如果配置了状态队列，还会启动一个定时任务定期发送心跳消息。
     *
     * @param mqParam    MQ 参数，包含队列名称、URI 等信息
     * @param mqListener 消息监听器，用于处理接收到的消息
     * @throws Exception 如果监听过程中发生错误
     */
    public final void listen(MqParam mqParam, MqListener mqListener) throws Exception {
        Assert.notNull(mqParam, "mqParam is null.");
        Assert.hasText(mqParam.getMqUri(), "mqParam.getMqUri() is blank.");
        Assert.hasText(mqParam.getInq(), "mqParam.getInq() is blank.");
        Assert.notNull(mqListener, "mqListener is null.");
        log.info("Listen begin [mqParam={}]", mqParam);

        String beanName = String.format("skynet.%s.consumer", mqParam.getMqType());
        if (!applicationContext.containsBean(beanName)) {
            throw new MessageServiceException(String.format("The MqConsumer bean [%s] not found.", beanName));
        }

        this.mqConsumer = applicationContext.getBean(beanName, MqConsumer.class);
        this.mqListener = mqListener;
        this.mqParam = mqParam;
        log.info("Pre Listen end [mqParam={}]", mqParam);

        new Thread(() -> {
            //等待 springboot 容器所有的 bean 初始化结束，再启动 mq 消费者。
            log.info("ApplicationReady Start No.{} MqConsumer listen...", MQ_CONSUMER_PROXY_INDEX.get());
            try {
                APPLICATION_READY_LATCH.await();
                // 包装监听器，支持取消任务
                MqListener consumerListener = wrapListenerIfCancel(mqParam, mqListener, beanName);
                String clientId = buildClientId(mqParam.getOutq(), mqParam.getInq());
                this.mqConsumer.listen(clientId, mqParam, consumerListener);
                if (StringUtils.isNoneBlank(mqParam.getStatusq())) {
                    this.reportStatus(clientId, mqParam);
                }
            } catch (Exception e) {
                log.error("Listen error.", e);
            }
        }).start();
    }

    /**
     * 包装监听器，支持取消任务
     */
    private MqListener wrapListenerIfCancel(MqParam mqParam, MqListener mqListener, String beanName) throws Exception {
        if (mqParam.getCancelOptions().isValid()) {
            log.info("Init CancelOptions= {}", mqParam.getCancelOptions());
            this.cancelMqConsumer = applicationContext.getBean(beanName, MqConsumer.class);

            // Guava 缓存：用于存储已取消的 taskId，TTL 3 小时，最大 100 条
            final Cache<String, String> taskIdCache = CacheBuilder.newBuilder()
                    .maximumSize(mqParam.getCancelOptions().getCacheMaxTaskSize())
                    .expireAfterWrite(mqParam.getCancelOptions().getCacheTtl())
                    .build();

            initCancelMessage(this.cancelMqConsumer, mqParam, taskIdCache);
            // 装饰监听器，拦截已取消的任务
            return new MqListener() {
                @Override
                public void onMessage(MqMessage message) throws Exception {
                    String taskId = message.getProperties().getOrDefault(mqParam.getCancelOptions().getTaskIdHeaderKey(), "unset");
                    if (taskIdCache.asMap().containsKey(taskId)) {
                        meterRegistry.counter("skynet.pandora.mq.handle.cancel.count").increment();
                        log.debug("TaskId cancelled, taskId={}", taskId);
                        log.trace("TaskId cancelled, message={}", message);
                    } else {
                        mqListener.onMessage(message);
                    }
                }

                @Override
                public void close() throws Exception {
                    mqListener.close();
                }
            };
        }
        return mqListener;
    }

    /**
     * 构建唯一 clientId
     */
    private String buildClientId(String outq, String inq) {
        String key = outq + Guid.randomGuid();
        return String.format("[a:%s]-[pid:%s,tid:%d]-[inq:%s]-[st:%s]-[rid:%s]",
                skynetProperties.getActionPoint(),
                OsUtil.getCurrentPid(), Thread.currentThread().threadId(), inq,
                new SimpleDateFormat("MMdd-HHmm").format(new Date()), Math.abs(key.hashCode()));
    }

    /**
     * 初始化 CancelTask 消费者
     */
    public void initCancelMessage(MqConsumer mqConsumer, MqParam mqParam, Cache<String, String> taskIdCache) throws Exception {
        log.info("Init CancelTask Consumer ...");
        MqParam cancelMqParam = mqParam.clone();
        cancelMqParam.setInq(cancelMqParam.getCancelOptions().getTopic());
        cancelMqParam.setConcurrency(1);
        cancelMqParam.setGroup(String.format("%s_%s_%s", cancelMqParam.getGroup(), skynetProperties.getIpAddress(), skynetProperties.getPort()));
        cancelMqParam.getConsumerProps().put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, "false");
        cancelMqParam.getConsumerProps().put(MqMessageConst.REWIND_OFFSET_COUNT, String.valueOf(mqParam.getCancelOptions().getCacheMaxTaskSize()));

        log.info("The cancel task topic={} group={} ...", cancelMqParam.getInq(), cancelMqParam.getGroup());
        String clientId = buildClientId(mqParam.getOutq(), cancelMqParam.getInq());
        MqListener consumerListener = message -> handleCancelMessage(message, taskIdCache);
        mqConsumer.listen(clientId, cancelMqParam, consumerListener);
        log.info("Init CancelTask Consumer end.");
    }

    /**
     * 处理取消消息
     */
    private void handleCancelMessage(MqMessage message, Cache<String, String> taskIdCache) throws Exception {
        log.debug("Receive ctrl message. traceId={}", message.getTraceId());
        MqMessageCtrl mqMessageCtrl = null;
        if (MqMessageConst.HEADER_MESSAGE_FORMAT_MSGPACK.equalsIgnoreCase(message.getProperty(MqMessageConst.HEADER_MESSAGE_FORMAT))) {
            mqMessageCtrl = objectMapper.readValue(message.getBody(), MqMessageCtrl.class);
        } else {
            try {
                mqMessageCtrl = JSON.parseObject(message.getBody(), MqMessageCtrl.class);
            } catch (Exception e) {
                log.error("""
                          Parse message error. message:
                          {}
                          {}
                          {}
                          {}
                          {}"""
                        , "-".repeat(30),
                        JSON.toJSONString(message.getProperties()),
                        "-".repeat(30),
                        new String(message.getBody(), StandardCharsets.UTF_8),
                        "-".repeat(30), e);
            }
        }
        // 只处理 CANCEL 消息
        if (mqMessageCtrl != null && mqMessageCtrl.isCancel()) {
            meterRegistry.counter("skynet.pandora.mq.handle.cancel.ctrl.message.count").increment();
            log.debug("TaskId cancelled, message={}", message);
            String taskId = mqMessageCtrl.getHeader().getTaskId();
            log.info("Receive Task cancelled, taskId={}", taskId);
            taskIdCache.put(taskId, taskId);
        }
    }

    /**
     * 启动状态报告定时任务
     * <p>
     * 该方法创建一个定时任务，定期向状态队列发送心跳消息，
     * 用于监控 MQ 消费者的运行状态。
     *
     * @param clientId 客户端 ID
     * @param mqParam  MQ 参数
     */
    private void reportStatus(String clientId, MqParam mqParam) {
        MqParam tempMqParam = mqParam.clone().setOutq(mqParam.getStatusq());
        // 启动定时器（如已存在则先关闭）
        if (statusReportExecutor != null && !statusReportExecutor.isShutdown()) {
            statusReportExecutor.shutdownNow();
        }
        statusReportExecutor = Executors.newScheduledThreadPool(1, Thread.ofVirtual().name("skynet.mq.status.report").factory());
        statusReportExecutor.scheduleWithFixedDelay(() -> {
            if (mqProperties.isStatusReportEnabled() && isRunning) {
                try {
                    MqMessageStatus mqMessageStatus = new MqMessageStatus(clientId, mqParam, skynetProperties.getActionPoint(), status);
                    MqMessage statusMqMessage = new MqMessage();
                    statusMqMessage.setBody(objectMapper.writeValueAsBytes(mqMessageStatus));
                    statusMqMessage.putProperty(MqMessageConst.HEADER_MESSAGE_FORMAT, MqMessageConst.HEADER_MESSAGE_FORMAT_MSGPACK);
                    pandoraMqProducer.send(tempMqParam, statusMqMessage);
                } catch (Exception e) {
                    log.error("Send status message error={}, status={}", e.getMessage(), status, e);
                }
            }
        }, mqProperties.getStatusReportPeriod().getSeconds(), mqProperties.getStatusReportPeriod().getSeconds(), TimeUnit.SECONDS);
    }

    /**
     * 关闭 MQ 消费者
     * <p>
     * 该方法停止 MQ 消费者的运行，关闭消息监听器，
     * 并释放相关资源。
     *
     * @throws Exception 如果关闭过程中发生错误
     */
    @Override
    public final void close() throws Exception {
        log.info("Close mq.action.consumer begin ...");
        this.isRunning = false;
        if (statusReportExecutor != null && !statusReportExecutor.isShutdown()) {
            statusReportExecutor.shutdownNow();
        }
        if (this.mqConsumer != null) {
            this.mqConsumer.stop();
            try {
                if (mqListener != null) {
                    mqListener.close();
                }
            } catch (InterruptedException e) {
                log.error("ManualResetEvent WaitOne error: {};", e.getMessage(), e);
            }
            this.mqConsumer.close();
        }
        if (this.cancelMqConsumer != null) {
            this.cancelMqConsumer.close();
            this.cancelMqConsumer = null;
        }
        log.info("Close mq.action.consumer end.");
    }

    /**
     * 设置 Spring 应用上下文
     * <p>
     * 该方法由 Spring 容器调用，用于注入应用上下文。
     *
     * @param applicationContext Spring 应用上下文
     * @throws BeansException 如果设置过程中发生错误
     */
    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public static void setApplicationReady() {
        APPLICATION_READY_LATCH.countDown();
    }
}

//
//
//        if (org.springframework.util.StringUtils.hasText(mqParam.getCancelOptions().getFilterCondition())) {
/// / 将对象包装成数组，为了统一使用过滤表达式 执行 JSONPath 表达式
//JSONArray result = (JSONArray) JSONPath.eval(new JSONArray(jsonObject), mqParam.getCancelOptions().getFilterCondition());
//            if (result == null || result.isEmpty()) {
//        log.debug("Filter condition not matched. traceId={}", message.getTraceId());
//        return;
//        }
//        }
//
//        meterRegistry.counter("skynet.pandora.mq.handle.cancel.ctrl.count").increment();
//        log.debug("TaskId cancelled, message={}", message);
//String taskId = String.valueOf(JSONPath.of(mqParam.getCancelOptions().getTaskIdJsonPath()).eval(jsonObject));
//        log.info("Receive Task cancelled, taskId={}", taskId);
//        taskIdCache.put(taskId, taskId);