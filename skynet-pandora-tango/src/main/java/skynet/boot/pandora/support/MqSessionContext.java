package skynet.boot.pandora.support;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import skynet.boot.SkynetProperties;
import skynet.boot.common.OsUtil;
import skynet.boot.pandora.api.ApiRequest;
import skynet.boot.pandora.api.ApiRequestObserver;
import skynet.boot.pandora.api.ApiResponse;
import skynet.boot.pandora.api.ApiResponseObserver;
import skynet.boot.pandora.api.mq.domain.MqMessage;
import skynet.boot.pandora.api.mq.domain.MqMessageConst;
import skynet.boot.pandora.ogma.PandoraApiRequestObserverBuilder;
import skynet.boot.pandora.ogma.tango.domain.MqMessageProps;

import java.net.ConnectException;
import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class MqSessionContext {

    private final PandoraApiRequestObserverBuilder pandoraApiRequestObserverBuilder;
    private final Map<String, String> fromHeader;
    private ApiRequestObserver apiRequestObserver;

    public MqSessionContext(SkynetProperties skynetProperties, PandoraApiRequestObserverBuilder pandoraApiRequestObserverBuilder) {
        this.pandoraApiRequestObserverBuilder = pandoraApiRequestObserverBuilder;

        this.fromHeader = new HashMap<>(3);
        this.fromHeader.put(MqMessageConst.HEADER_FROM_PID, String.valueOf(OsUtil.getCurrentPid()));
        this.fromHeader.put(MqMessageConst.HEADER_FROM_IP, skynetProperties.getIpAddress());
        this.fromHeader.put(MqMessageConst.HEADER_FROM_HOST, OsUtil.getHostName());
    }

    /**
     * 设置当前线程的 MQ 请求参数和消息。
     *
     * @param mqMessage 当前线程的 MQ 消息，
     */
    void init(MqMessage mqMessage) throws URISyntaxException, ConnectException {
        assert mqMessage != null;

        MqMessage targetMessage = new MqMessage();
        targetMessage.getProperties().putAll(mqMessage.getProperties());
        targetMessage.getProperties().remove(MqMessageConst.HEADER_TARGET_ENDPOINT);
        targetMessage.getProperties().putAll(fromHeader);

        //同步或异步 投递到调度的端点
        String dispatchEndpoint = targetMessage.getProperty(MqMessageConst.HEADER_CALLBACK_URI);
        if (pandoraApiRequestObserverBuilder != null && StringUtils.isNotBlank(dispatchEndpoint)
                && ((dispatchEndpoint.toLowerCase().startsWith("mq://")
                || dispatchEndpoint.toLowerCase().startsWith("http://")
                || dispatchEndpoint.toLowerCase().startsWith("https://")))) {
            targetMessage.getProperties().remove(MqMessageConst.HEADER_CALLBACK_URI);

            //全部返回
            Map<String, String> header = targetMessage.getProperties();
            this.apiRequestObserver = pandoraApiRequestObserverBuilder.build(dispatchEndpoint, header, new ApiResponseObserver() {
                @Override
                public void onReceive(ApiResponse response) {
                    log.debug("OnReceive = {}", response);
                }

                @Override
                public void onError(Throwable throwable) {
                    log.error("OnError = {}", throwable.getMessage());
                }

                @Override
                public void onCompleted() {
                    log.debug("OnCompleted");
                }
            });
        } else {
            this.apiRequestObserver = null;
        }
    }

    /**
     * 发送带消息头的 MQ 消息
     *
     * @param apiResponse 响应对象
     * @param headers     消息头
     * @return 是否发送成功
     */
    public boolean send(ApiResponse apiResponse, Map<String, String> headers) throws Exception {
        if (apiResponse != null && this.apiRequestObserver != null) {
            ApiRequest apiRequest = new ApiRequest();
            apiRequest.setPayload(JSONObject.from(apiResponse));
            this.apiRequestObserver.onSend(apiRequest, new MqMessageProps(headers));
            return true;
        }
        return false;
    }

    /**
     * 发送 MQ 消息
     *
     * @param apiResponse 响应对象
     * @return 是否发送成功
     */
    public boolean send(ApiResponse apiResponse) throws Exception {
        return send(apiResponse, Map.of());
    }

    void close() throws Exception {
        if (this.apiRequestObserver != null) {
            this.apiRequestObserver.onCompleted();
        }
    }
}
