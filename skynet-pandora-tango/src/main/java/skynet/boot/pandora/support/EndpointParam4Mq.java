package skynet.boot.pandora.support;

import lombok.Getter;
import lombok.Setter;
import skynet.boot.pandora.MqServiceEndpoint;
import skynet.boot.pandora.annotation.SkynetPandoraMqEndpoint;
import skynet.boot.pandora.api.ApiProtocol;
import skynet.boot.pandora.config.EndpointProperties;
import skynet.boot.pandora.service.support.data.EndpointParam;


@Setter
@Getter
public class EndpointParam4Mq extends EndpointParam<MqServiceEndpoint> {
    public EndpointParam4Mq(MqServiceEndpoint serviceEndpoint, SkynetPandoraMqEndpoint annotation) {
        super(annotation.endpointName(), annotation.endpointName(), ApiProtocol.MQ, serviceEndpoint, annotation.serviceHandler());
    }

    private EndpointProperties endpointProperties;

    @Override
    public String toString() {
        return super.toString();
    }

    @Override
    public boolean equals(Object o) {
        return super.equals(o);
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }
}